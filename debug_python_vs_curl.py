#!/usr/bin/env python3
"""
Debug script to compare Python requests vs curl behavior
"""

import requests
import urllib3

# Disable SSL warnings for debugging
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configuration - exact same as working curl
BASE_URL = "http://************:8913/topupservice/service"
RESELLER_ID = "DIST2348077469471"
USER_ID = "9900"
TEST_MSISDN = "*************"
PASSWORD = "mmt123mmt234"

def debug_airtime_request():
    """Debug the exact airtime request that works in curl"""
    
    # Exact headers from working curl
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    # Exact XML body from working curl (no f-string formatting issues)
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    print("🔍 DEBUG INFO:")
    print(f"URL: {BASE_URL}")
    print(f"Headers: {headers}")
    print(f"Body length: {len(soap_body)} characters")
    print(f"Body preview: {soap_body[:100]}...")
    
    try:
        print(f"\n📡 Sending request to: {BASE_URL}")
        response = requests.post(
            url=BASE_URL,
            data=soap_body,
            headers=headers,
            timeout=30,
            verify=False  # Disable SSL verification for debugging
        )
        
        print(f"✅ Response received!")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        return response
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        print(f"Exception type: {type(e).__name__}")
        return None

def debug_with_session():
    """Try with a requests session to see if that helps"""
    
    session = requests.Session()
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    try:
        print(f"\n📡 Sending request with session...")
        response = session.post(
            url=BASE_URL,
            data=soap_body,
            headers=headers,
            timeout=30
        )
        
        print(f"✅ Session response received!")
        print(f"Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")
        
        return response
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Session request failed: {e}")
        return None

def test_url_variations():
    """Test different URL variations to see what works"""
    
    urls_to_test = [
        "http://************:8913/topupservice/service",
        "http://************:8913/topupservice/service/",
        "http://************:8913/topupservice",
        "http://************:8913/",
    ]
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    for i, url in enumerate(urls_to_test, 1):
        print(f"\n{i}. Testing URL: {url}")
        try:
            response = requests.post(url, data=soap_body, headers=headers, timeout=10)
            print(f"   ✅ Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   🎉 SUCCESS! Response: {response.text[:100]}...")
            else:
                print(f"   ⚠️  Response: {response.text[:100]}...")
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🐛 Python vs Curl Debug Session")
    print("=" * 50)
    
    # Test 1: Basic request
    print("\n1️⃣ Testing basic request (same as curl):")
    debug_airtime_request()
    
    # Test 2: With session
    print("\n2️⃣ Testing with requests session:")
    debug_with_session()
    
    # Test 3: URL variations
    print("\n3️⃣ Testing different URL variations:")
    test_url_variations()
    
    print("\n" + "=" * 50)
    print("🔍 Debug session complete!")
