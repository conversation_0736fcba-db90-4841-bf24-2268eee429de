VOT TYPE OF VOD: This requires a valid receiver msisdn to be entered which in this case must be the reseller's msisdn.
---------------------------------------------------------------------------------------------------------------------

<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>12q31a1a456677881</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348058555399</id>
               <type>RESELLERUSER</type>
   			<userId>9900</userId>
            </initiatorPrincipalId>
            <password>*******</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOT</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>1</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>DIST2348058555399 </id>
            <type>RESELLERUSER</type>
			<userId>9900</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
         <id>*************</id>
         <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOT</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>



VOT TYPE OF VOD RESPONSE:
--------------------------
<soapEnvelope xmlnssoap="http//schemas.xmlsoap.org/soap/envelope/">
  <soapBody>
    <ns2requestPurchaseResponse xmlnsns2="http//external.interfaces.ers.seamless.com/">
      <return>
        <ersReference>2016112118082084401000009</ersReference>
        <resultCode>0</resultCode>
        <resultDescription>You have sold one voucher- 
          Amount 1.0 NGN
          Pin ****************
          Expiry Date 24-11-2016
          Ref.2016112118082084401000009
          Serial*************</resultDescription>
        <receiverPrincipal>
          <principalId>
            <id>*************</id>
            <type>SUBSCRIBERID</type>
          </principalId>
          <principalName></principalName>
          <accounts>
            <account>
              <accountSpecifier>
                <accountId>*************</accountId>
                <accountTypeId>AIRTIME</accountTypeId>
              </accountSpecifier>
            </account>
          </accounts>
        </receiverPrincipal>
        <purchasedProducts>
          <productSpecifier>
            <productId>VOT</productId>
            <productIdType>PRODUCT_SKU</productIdType>
          </productSpecifier>
          <purchaseCount>1</purchaseCount>
          <productDetails>
            <map>
              <entry>
                <key>SERIAL</key>
                <value>*************</value>
              </entry>
              <entry>
                <key>EXPIRY_DATE</key>
                <value>Thu Nov 24 000000 WAT 2016</value>
              </entry>
            </map>
          </productDetails>
        </purchasedProducts>
      </return>
    </ns2requestPurchaseResponse>
  </soapBody>
</soapEnvelope>