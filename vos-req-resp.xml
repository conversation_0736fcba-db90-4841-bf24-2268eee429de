VOS TYPE OF VOD webmethod for VOS service is requestPurchase:
-------------------------------------------------------------

<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>123456</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348058555399</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>********</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOS</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>3</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>DIST2348058555399 </id>
            <type>RESELLERUSER</type>
			<userId>9900</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
         <id>*************</id>
         <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOS</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>


VOS type of VOD SUCCESS RESPONSE  PurchaseResponse:
-----------------------------------------------------
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><ns2:requestPurchaseResponse xmlns:ns2="http://external.interfaces.ers.seamless.com/">
<return>
<ersReference>2017052517181329501000475</ersReference>
<resultCode>0</resultCode>
<resultDescription>You have sold one voucher:- <br />
Amount: 100.0 NGN<br />
Expiry Date: 28-05-2017<br />
Ref.:2017052517181329501000475<br />
Balance:  **** NGN.<br />
Serial:*************</resultDescription>
<receiverPrincipal>
<principalId>
<id>*************</id>
<type>SUBSCRIBERID</type>
</principalId>
<principalName></principalName>
<accounts>
<account>
<accountSpecifier>
<accountId>*************</accountId>
<accountTypeId>AIRTIME</accountTypeId>
</accountSpecifier>
</account>
<account><accountSpecifier>
<accountId>*************</accountId>
<accountTypeId>DATA_BUNDLE</accountTypeId>
</accountSpecifier>
</account>
</accounts>
</receiverPrincipal>
<purchasedProducts>
<productSpecifier>
<productId>VOS</productId>
<productIdType>PRODUCT_SKU</productIdType>
</productSpecifier>
<purchaseCount>1</purchaseCount>
<productDetails>
<map>
<entry>
<key>SERIAL</key>
<value>*************</value>
</entry>
<entry>
<key>EXPIRY_DATE</key>
<value>Sun May 28 00:00:00 WAT 2017</value>
</entry>
</map>
</productDetails>
</purchasedProducts>
</return>
</ns2:requestPurchaseResponse>
</soap:Body>
</soap:Envelope>