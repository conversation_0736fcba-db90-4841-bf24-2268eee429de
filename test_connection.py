#!/usr/bin/env python3
"""
Simple connection test to verify if we can reach the server
"""

import requests
import socket

def test_basic_connection():
    """Test basic TCP connection to the server"""
    try:
        print("🔌 Testing TCP connection to ************:8913...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(('************', 8913))
        sock.close()
        
        if result == 0:
            print("✅ TCP connection successful!")
            return True
        else:
            print(f"❌ TCP connection failed with error code: {result}")
            return False
    except Exception as e:
        print(f"❌ TCP connection error: {e}")
        return False

def test_http_get():
    """Test basic HTTP GET to see if server responds"""
    try:
        print("🌐 Testing HTTP GET to base URL...")
        response = requests.get(
            'http://************:8913/',
            timeout=10,
            headers={'User-Agent': 'curl/7.68.0'}
        )
        print(f"✅ HTTP GET successful! Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        return True
    except Exception as e:
        print(f"❌ HTTP GET failed: {e}")
        return False

def test_soap_endpoint():
    """Test the SOAP endpoint specifically"""
    try:
        print("🧼 Testing SOAP endpoint...")
        response = requests.get(
            'http://************:8913/topupservice/service',
            timeout=10,
            headers={'User-Agent': 'curl/7.68.0'}
        )
        print(f"✅ SOAP endpoint accessible! Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        return True
    except Exception as e:
        print(f"❌ SOAP endpoint failed: {e}")
        return False

def test_with_curl_headers():
    """Test POST with exact curl headers"""
    try:
        print("📡 Testing POST with curl-like headers...")
        
        headers = {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': '""',
            'User-Agent': 'curl/7.68.0',
            'Accept': '*/*'
        }
        
        # Simple XML body
        soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
        
        response = requests.post(
            'http://************:8913/topupservice/service',
            data=soap_body,
            headers=headers,
            timeout=15
        )
        
        print(f"✅ POST successful! Status: {response.status_code}")
        print(f"Response: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ POST failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Connection Test Suite")
    print("=" * 40)
    
    # Run all tests
    tests = [
        ("TCP Connection", test_basic_connection),
        ("HTTP GET", test_http_get),
        ("SOAP Endpoint", test_soap_endpoint),
        ("SOAP POST", test_with_curl_headers),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n🔍 {name}:")
        result = test_func()
        results.append((name, result))
        print("-" * 40)
    
    print(f"\n📊 Test Results Summary:")
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {name}: {status}")
