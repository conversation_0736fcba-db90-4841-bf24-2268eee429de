#!/usr/bin/env python3
"""
Individual Glovas API Request Examples
Each function can be called independently for testing specific operations.
"""

import requests

# Configuration
BASE_URL = "http://41.203.65.11:8913/topupservice/service"  # TEST (WORKING)
# PROD_URL = "http://41.203.65.10:8913/topupservice/service"  # PRODUCTION
RESELLER_ID = "DIST2348077469471"
USER_ID = "9900"
TEST_MSISDN = "2348059999008"
PASSWORD = "mmt123mmt234"

def airtime_topup_1_naira():
    """Airtime topup request - 1 naira"""

    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'  # Empty SOAPAction (working format)
    }

    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID}</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""

    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def data_bundle_25_naira():
    """Data bundle request - 25 naira"""

    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'  # Empty SOAPAction (working format)
    }

    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>?</clientComment>
            <clientId>ERS</clientId>
            <prepareOnly>false</prepareOnly>
            <clientReference>55555</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
            <transactionProperties>
               <entry>
                  <key>TRANSACTION_TYPE</key>
                  <value>PRODUCT_RECHARGE</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID}</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>DATA_BUNDLE</accountTypeId>
         </topupAccountSpecifier>
         <productId>DATA-32</productId>
         <amount>
            <currency>NGN</currency>
            <value>25</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""

    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def vos_purchase_3_naira():
    """VOS (Voucher On Sale) purchase request - 3 naira"""

    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'  # Empty SOAPAction (working format)
    }

    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>123456</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOS</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>3</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOS</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>"""

    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def vot_purchase_3_naira():
    """VOT (Voucher On Transfer) purchase request - 3 naira"""

    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'  # Empty SOAPAction (working format)
    }

    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>12q31a1a456677881</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOT</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>3</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOT</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>"""

    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

# Example usage
if __name__ == "__main__":
    print("Testing individual requests...")

    # Test airtime topup
    print("\n1. Testing Airtime Topup (1 Naira)...")
    try:
        response = airtime_topup_1_naira()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:1000]}...")
    except Exception as e:
        print(f"Error: {e}")

    # Test data bundle
    print("\n2. Testing Data Bundle (25 Naira)...")
    try:
        response = data_bundle_25_naira()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:1000]}...")
    except Exception as e:
        print(f"Error: {e}")

    # Test VOS purchase
    print("\n3. Testing VOS Purchase (3 Naira)...")
    try:
        response = vos_purchase_3_naira()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:1000]}...")
    except Exception as e:
        print(f"Error: {e}")

    # Test VOT purchase
    print("\n4. Testing VOT Purchase (3 Naira)...")
    try:
        response = vot_purchase_3_naira()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:1000]}...")
    except Exception as e:
        print(f"Error: {e}")
