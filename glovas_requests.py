#!/usr/bin/env python3
"""
Glovas API Python Requests
Based on XML samples with test amounts:
- Airtime: 1 naira
- Data: 25 naira
- VOS: 3 naira
- VOT: 3 naira

Base URL: 41.203.65.10:8913
"""

import requests
import xml.etree.ElementTree as ET
from typing import Dict, Any

# Configuration
BASE_URL = "http://41.203.65.11:8913/topupservice/service"  # TEST (WORKING)
# PROD_URL = "http://41.203.65.10:8913/topupservice/service"  # PRODUCTION
SOAP_ACTION_TOPUP = '""'  # Empty SOAPAction (working format)
SOAP_ACTION_PURCHASE = '""'  # Empty SOAPAction (working format)

# Test credentials and numbers from XML samples
RESELLER_ID = "DIST2348077469471"  # Updated from topup-req-resp (3).xml - Whispa Connect Limited
USER_ID = "9900"
TEST_MSISDN = "2348059999008"
PASSWORD = "mmt123mmt234"

def create_soap_headers(soap_action: str) -> Dict[str, str]:
    """Create SOAP headers for requests"""
    return {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': soap_action,
        'Accept': 'text/xml'
    }

def airtime_topup_request(amount: float = 1.0) -> str:
    """
    Create SOAP request for airtime topup
    Test amount: 1 naira
    """
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID}</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>{amount}</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    return soap_body

def data_bundle_request(amount: float = 25.0) -> str:
    """
    Create SOAP request for data bundle
    Test amount: 25 naira
    """
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
 <soapenv:Header/>
 <soapenv:Body>
    <ext:requestTopup>
       <context>
          <channel>WSClient</channel>
          <clientComment>?</clientComment>
          <clientId>ERS</clientId>
          <prepareOnly>false</prepareOnly>
          <clientReference>55555</clientReference>
          <clientRequestTimeout>500</clientRequestTimeout>
          <initiatorPrincipalId>
             <id>{RESELLER_ID}</id>
             <type>RESELLERUSER</type>
             <userId>{USER_ID}</userId>
          </initiatorPrincipalId>
          <password>{PASSWORD}</password>
          <transactionProperties>
             <entry>
                <key>TRANSACTION_TYPE</key>
                <value>PRODUCT_RECHARGE</value>
             </entry>
          </transactionProperties>
       </context>
       <senderPrincipalId>
          <id>{RESELLER_ID}</id>
          <type>RESELLERUSER</type>
          <userId>{USER_ID}</userId>
       </senderPrincipalId>
       <topupPrincipalId>
          <id>{TEST_MSISDN}</id>
          <type>SUBSCRIBERMSISDN</type>
          <userId></userId>
       </topupPrincipalId>
       <senderAccountSpecifier>
          <accountId>{RESELLER_ID}</accountId>
          <accountTypeId>RESELLER</accountTypeId>
       </senderAccountSpecifier>
       <topupAccountSpecifier>
          <accountId>{TEST_MSISDN}</accountId>
          <accountTypeId>DATA_BUNDLE</accountTypeId>
       </topupAccountSpecifier>
       <productId>DATA-32</productId>
       <amount>
          <currency>NGN</currency>
          <value>{amount}</value>
       </amount>
    </ext:requestTopup>
 </soapenv:Body>
</soapenv:Envelope>"""
    return soap_body

def vos_purchase_request(amount: float = 3.0) -> str:
    """
    Create SOAP request for VOS (Voucher On Sale) purchase
    Test amount: 3 naira
    """
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>123456</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOS</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>{amount}</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOS</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>"""
    return soap_body

def vot_purchase_request(amount: float = 3.0) -> str:
    """
    Create SOAP request for VOT (Voucher On Transfer) purchase
    Test amount: 3 naira
    """
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>12q31a1a456677881</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOT</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>{amount}</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOT</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>"""
    return soap_body

def send_soap_request(soap_body: str, soap_action: str) -> requests.Response:
    """Send SOAP request to the SOAP service endpoint"""
    headers = create_soap_headers(soap_action)

    try:
        response = requests.post(
            url=BASE_URL,
            data=soap_body,
            headers=headers,
            timeout=30
        )
        return response
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        raise

def parse_soap_response(response: requests.Response) -> Dict[str, Any]:
    """Parse SOAP response and extract key information"""
    try:
        root = ET.fromstring(response.text)

        # Find the return element
        return_elem = root.find('.//{http://external.interfaces.ers.seamless.com/}return')

        if return_elem is not None:
            result = {}

            # Extract common fields
            for field in ['ersReference', 'resultCode', 'resultDescription']:
                elem = return_elem.find(f'.//{field}')
                if elem is not None:
                    result[field] = elem.text

            return result
        else:
            return {"error": "Could not parse response", "raw_response": response.text}

    except ET.ParseError as e:
        return {"error": f"XML parsing error: {e}", "raw_response": response.text}

# Main execution functions
def perform_airtime_topup():
    """Perform airtime topup with test amount (1 naira)"""
    print("=== AIRTIME TOPUP (1 Naira) ===")
    soap_body = airtime_topup_request(1.0)
    response = send_soap_request(soap_body, SOAP_ACTION_TOPUP)

    print(f"Status Code: {response.status_code}")
    result = parse_soap_response(response)
    print(f"Response: {result}")
    return response

def perform_data_bundle():
    """Perform data bundle purchase with test amount (25 naira)"""
    print("\n=== DATA BUNDLE (25 Naira) ===")
    soap_body = data_bundle_request(25.0)
    response = send_soap_request(soap_body, SOAP_ACTION_TOPUP)

    print(f"Status Code: {response.status_code}")
    result = parse_soap_response(response)
    print(f"Response: {result}")
    return response

def perform_vos_purchase():
    """Perform VOS purchase with test amount (3 naira)"""
    print("\n=== VOS PURCHASE (3 Naira) ===")
    soap_body = vos_purchase_request(3.0)
    response = send_soap_request(soap_body, SOAP_ACTION_PURCHASE)

    print(f"Status Code: {response.status_code}")
    result = parse_soap_response(response)
    print(f"Response: {result}")
    return response

def perform_vot_purchase():
    """Perform VOT purchase with test amount (3 naira)"""
    print("\n=== VOT PURCHASE (3 Naira) ===")
    soap_body = vot_purchase_request(3.0)
    response = send_soap_request(soap_body, SOAP_ACTION_PURCHASE)

    print(f"Status Code: {response.status_code}")
    result = parse_soap_response(response)
    print(f"Response: {result}")
    return response

if __name__ == "__main__":
    print("Glovas API Test Requests")
    print("========================")
    print(f"Base URL: {BASE_URL}")
    print(f"Test MSISDN: {TEST_MSISDN}")
    print(f"Reseller ID: {RESELLER_ID}")
    print()

    try:
        # Perform all test requests
        perform_airtime_topup()
        perform_data_bundle()
        perform_vos_purchase()
        perform_vot_purchase()

    except Exception as e:
        print(f"Error occurred: {e}")
