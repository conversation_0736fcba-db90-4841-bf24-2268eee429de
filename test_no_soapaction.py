#!/usr/bin/env python3
"""
Test request without SOAPAction header to troubleshoot the SOAP fault
"""

import requests

# Configuration
BASE_URL = "http://41.203.65.10:8913/topupservice/service"
RESELLER_ID = "DIST2348077469471"
USER_ID = "9900"
TEST_MSISDN = "2348059999008"
PASSWORD = "mmt123mmt234"

def test_without_soapaction():
    """Test airtime topup without SOAPAction header"""
    
    # Headers without SOAPAction
    headers = {
        'Content-Type': 'text/xml; charset=utf-8'
    }
    
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>*********</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID}</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def test_with_empty_soapaction():
    """Test airtime topup with empty SOAPAction"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>*********</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID}</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def test_with_alternative_soapaction():
    """Test with alternative SOAPAction format"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': 'requestTopup'  # Simple action name
    }
    
    soap_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>*********</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{PASSWORD}</password>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID}</id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID}</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

if __name__ == "__main__":
    print("Testing different SOAPAction approaches...")
    
    print("\n1. Testing WITHOUT SOAPAction header:")
    try:
        response = test_without_soapaction()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n2. Testing WITH EMPTY SOAPAction:")
    try:
        response = test_with_empty_soapaction()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n3. Testing WITH SIMPLE SOAPAction:")
    try:
        response = test_with_alternative_soapaction()
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
