<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
 <soapenv:Header/>
 <soapenv:Body>
    <ext:requestTopup>
       <!--Optional:-->
       <context>
          <!--Optional:-->
          <channel>WSClient</channel>
          <!--Optional:-->
          <clientComment>?</clientComment>
          <!--Optional:-->
          <clientId>ERS</clientId>
          <!--Optional:-->
          <prepareOnly>false</prepareOnly>
          <!--Optional:-->
          <clientReference>55555</clientReference>
          <clientRequestTimeout>500</clientRequestTimeout>
          <!--Optional:-->
          <initiatorPrincipalId>
             <!--Optional:-->
             <id>DIST2348058555399</id>
             <!--Optional:-->
             <type>RESELLERUSER</type>
             <!--Optional:-->
             <userId>9900</userId>
          </initiatorPrincipalId>
          <!--Optional:-->
          <password>********</password>
          <!--Optional:-->
          <transactionProperties>
             <!--Zero or more repetitions:-->
             <entry>
                <!--Optional:-->
                <key>TRANSACTION_TYPE</key>
                <!--Optional:-->
                <value>PRODUCT_RECHARGE</value>
             </entry>
          </transactionProperties>
       </context>
       <!--Optional:-->
       <senderPrincipalId>
          <!--Optional:-->
          <id>DIST2348058555399 </id>
          <!--Optional:-->
          <type>RESELLERUSER</type>
          <!--Optional:-->
          <userId>9900</userId>
       </senderPrincipalId>
       <!--Optional:-->
       <topupPrincipalId>
          <!--Optional:-->
          <id>*************</id>
          <!--Optional:-->
          <type>SUBSCRIBERMSISDN</type>
          <!--Optional:-->
          <userId></userId>
       </topupPrincipalId>
       <!--Optional:-->
       <senderAccountSpecifier>
          <!--Optional:-->
          <accountId>DIST2348058555399 </accountId>
          <!--Optional:-->
          <accountTypeId>RESELLER</accountTypeId>
       </senderAccountSpecifier>
       <!--Optional:-->
       <topupAccountSpecifier>
          <!--Optional:-->
          <accountId>*************</accountId>
          <!--Optional:-->
          <accountTypeId>DATA_BUNDLE</accountTypeId>
       </topupAccountSpecifier>
       <!--Optional:-->
       <productId>DATA-32</productId>
       <!--Optional:-->
       <amount>
          <!--Optional:-->
          <currency>NGN</currency>
          <!--Optional:-->
          <value>25</value>
       </amount>
    </ext:requestTopup>
 </soapenv:Body>
</soapenv:Envelope>





data successful response:

<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
   <soap:Body>
      <ns2:requestTopupResponse xmlns:ns2="http://external.interfaces.ers.seamless.com/">
         <return>
            <ersReference>2017072015152930902091677</ersReference>
            <resultCode>0</resultCode>
            <resultDescription>SUCCESS : You have sold N25= 10MB 1 Day worth  25.00 NGN to *************. Your balance is now ****** NGN. ref: 2017072015152930902091677</resultDescription>
            <requestedTopupAmount>
               <currency>NGN</currency>
               <value>25</value>
            </requestedTopupAmount>
            <senderPrincipal>
               <principalId>
                  <id>DIST2348058555399</id>
                  <type>RESELLERID</type>
               </principalId>
               <principalName>OLUMAJEK IT CONTINENTAL ABEOKUTA</principalName>
               <accounts>
                  <account>
                     <accountDescription>OLUMAJEK IT CONTINENTAL ABEOKUTA</accountDescription>
                     <accountSpecifier>
                        <accountId>DIST2348058555399</accountId>
                        <accountTypeId>RESELLER</accountTypeId>
                     </accountSpecifier>
                     <balance>
                        <currency>NGN</currency>
                        <value>********</value>
                     </balance>
                     <creditLimit>
                        <currency>NGN</currency>
                        <value>0.00000</value>
                     </creditLimit>
                  </account>
               </accounts>
               <status>Active</status>
               <msisdn>*************</msisdn>
            </senderPrincipal>
            <topupAccountSpecifier>
               <accountId>*************</accountId>
               <accountTypeId>DATA_BUNDLE</accountTypeId>
            </topupAccountSpecifier>
            <topupAmount>
               <currency>NGN</currency>
               <value>25</value>
            </topupAmount>
            <topupPrincipal>
               <principalId>
                  <id>*************</id>
                  <type>SUBSCRIBERID</type>
               </principalId>
               <principalName/>
               <accounts>
                  <account>
                     <accountSpecifier>
                        <accountId>*************</accountId>
                        <accountTypeId>AIRTIME</accountTypeId>
                     </accountSpecifier>
                  </account>
                  <account>
                     <accountSpecifier>
                        <accountId>*************</accountId>
                        <accountTypeId>DATA_BUNDLE</accountTypeId>
                     </accountSpecifier>
                  </account>
               </accounts>
            </topupPrincipal>
         </return>
      </ns2:requestTopupResponse>
   </soap:Body>
</soap:Envelope>