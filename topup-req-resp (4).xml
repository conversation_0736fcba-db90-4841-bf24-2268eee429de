TOPUP REQUEST
------------

<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <!--Optional:-->
         <context>
            <!--Optional:-->
            <channel>WSClient</channel>
            <!--Optional:-->
            <clientComment>test xml for ers</clientComment>
            <!--Optional:-->
            <clientId>ERS</clientId>
            <!--Optional:-->
            <clientReference>*********</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <!--Optional:-->
            <initiatorPrincipalId>
               <!--reseller id for parent:-->
               <id>DIST2348077469471</id>
               <!--Optional:-->
               <type>RESELLERUSER</type>
               <!--Optional:-->
               <userId>9900</userId>
            </initiatorPrincipalId>
            <!--password for parent:-->
            <password>*********</password>
         </context>
         <!--Optional:-->
         <senderPrincipalId>
            <!--reseleer id for parent:-->
            <id>DIST2348077469471 </id>
            <!--Optional:-->
            <type>RESELLERUSER</type>
            <!--user for the reseller:-->
            <userId>9900</userId>
         </senderPrincipalId>
         <!--Optional:-->
         <topupPrincipalId>
            <!--user to be topup:-->
            <id>*************</id>
            <!--Optional:-->
            <type>SUBSCRIBERMSISDN</type>
            <!--Optional:-->
            <userId>?</userId>
         </topupPrincipalId>
         <!--Optional:-->
         <senderAccountSpecifier>
            <!--reselleer id for parent:-->
            <accountId>DIST2348077469471 </accountId>
            <!--Optional:-->
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <!--Optional:-->
         <topupAccountSpecifier>
            <!--user to be toped up:-->
            <accountId>*************</accountId>
            <!--Optional:-->
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <!--Optional:-->
         <productId>TOPUP</productId>
         <!--Optional:-->
         <amount>
            <!--currency to be toped up:-->
            <currency>NGN</currency>
            <!--amount to be toped up:-->
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>




TOPUP RESPONSE:
---------------

<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
   <soap:Body>
      <ns2:requestTopupResponse xmlns:ns2="http://external.interfaces.ers.seamless.com/">
         <return>
            <ersReference>2016041517295403001000345</ersReference>
            <resultCode>0</resultCode>
            <resultDescription>SUCCESS</resultDescription>
            <requestedTopupAmount>
               <currency>NGN</currency>
               <value>1</value>
            </requestedTopupAmount>
            <senderPrincipal>
               <principalId>
                  <id>DIST2348077469471 </id>
                  <type>RESELLERID</type>
               </principalId>
               <principalName>Whispa Connect Limited</principalName>
               <accounts>
                  <account>
                     <accountDescription>Whispa Connect Limited</accountDescription>
                     <accountSpecifier>
                        <accountId>DIST2348077469471 </accountId>
                        <accountTypeId>RESELLER</accountTypeId>
                     </accountSpecifier>
                     <balance>
                        <currency>NGN</currency>
                        <value>3.00</value>
                     </balance>
                     <creditLimit>
                        <currency>NGN</currency>
                        <value>0.00000</value>
                     </creditLimit>
                  </account>
               </accounts>
               <status>Active</status>
               <msisdn>*************</msisdn>
            </senderPrincipal>
            <topupAccountSpecifier>
               <accountId>*************</accountId>
               <accountTypeId>AIRTIME</accountTypeId>
            </topupAccountSpecifier>
            <topupAmount>
               <currency>NGN</currency>
               <value>1.00</value>
            </topupAmount>
            <topupPrincipal>
               <principalId>
                  <id>*************</id>
                  <type>SUBSCRIBERID</type>
               </principalId>
               <principalName/>
               <accounts>
                  <account>
                     <accountSpecifier>
                        <accountId>*************</accountId>
                        <accountTypeId>AIRTIME</accountTypeId>
                     </accountSpecifier>
                  </account>
               </accounts>
            </topupPrincipal>
         </return>
      </ns2:requestTopupResponse>
   </soap:Body>
</soap:Envelope>




curl -X POST \
  'http://************:8913/topupservice/service' \
  -H 'Content-Type: text/xml; charset=utf-8' \
  -H 'SOAPAction: ""' \
  -d '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>*********</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>'