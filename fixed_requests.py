#!/usr/bin/env python3
"""
Fixed individual requests that exactly match the working curl command
"""

import requests

# Configuration - exact same as working curl
BASE_URL = "http://41.203.65.11:8913/topupservice/service"

def airtime_topup_1_naira_fixed():
    """Fixed airtime topup request - exact copy of working curl"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    # Exact XML from working curl command - no f-string variables
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def data_bundle_25_naira_fixed():
    """Fixed data bundle request - 25 naira"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>?</clientComment>
            <clientId>ERS</clientId>
            <prepareOnly>false</prepareOnly>
            <clientReference>55555</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
            <transactionProperties>
               <entry>
                  <key>TRANSACTION_TYPE</key>
                  <value>PRODUCT_RECHARGE</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId></userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>DIST2348077469471</accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>*************</accountId>
            <accountTypeId>DATA_BUNDLE</accountTypeId>
         </topupAccountSpecifier>
         <productId>DATA-32</productId>
         <amount>
            <currency>NGN</currency>
            <value>25</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def vos_purchase_3_naira_fixed():
    """Fixed VOS purchase request - 3 naira"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>123456</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOS</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>3</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOS</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def vot_purchase_3_naira_fixed():
    """Fixed VOT purchase request - 3 naira"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '""'
    }
    
    soap_body = """<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestPurchase>
         <context>
            <channel>WSClient</channel>
            <prepareOnly>false</prepareOnly>
            <clientReference>12q31a1a456677881</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>DIST2348077469471</id>
               <type>RESELLERUSER</type>
               <userId>9900</userId>
            </initiatorPrincipalId>
            <password>mmt123mmt234</password>
            <transactionProperties>
               <entry>
                  <key>preferredLanguage</key>
                  <value>en</value>
               </entry>
               <entry>
                  <key>productSKU</key>
                  <value>VOT</value>
               </entry>
               <entry>
                  <key>currency</key>
                  <value>NGN</value>
               </entry>
               <entry>
                  <key>purchaseAmount</key>
                  <value>3</value>
               </entry>
            </transactionProperties>
         </context>
         <senderPrincipalId>
            <id>DIST2348077469471</id>
            <type>RESELLERUSER</type>
            <userId>9900</userId>
         </senderPrincipalId>
         <receiverPrincipalId>
            <id>*************</id>
            <type>SUBSCRIBERMSISDN</type>
         </receiverPrincipalId>
         <senderAccountSpecifier>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <purchaseOrder>
            <productSpecifier>
               <productId>VOT</productId>
               <productIdType>VOD</productIdType>
            </productSpecifier>
            <purchaseCount>1</purchaseCount>
         </purchaseOrder>
      </ext:requestPurchase>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

# Test functions
def test_all_fixed_functions():
    """Test all fixed functions"""
    
    functions = [
        ("Airtime Topup (1 Naira)", airtime_topup_1_naira_fixed),
        ("Data Bundle (25 Naira)", data_bundle_25_naira_fixed),
        ("VOS Purchase (3 Naira)", vos_purchase_3_naira_fixed),
        ("VOT Purchase (3 Naira)", vot_purchase_3_naira_fixed),
    ]
    
    for name, func in functions:
        print(f"\n🧪 Testing {name}:")
        try:
            response = func()
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ SUCCESS!")
                print(f"   Response: {response.text}")
            else:
                print(f"   ❌ Error Response: {response.text[:200]}...")
        except Exception as e:
            print(f"   💥 Exception: {e}")

if __name__ == "__main__":
    print("🔧 Testing Fixed Request Functions")
    print("=" * 50)
    test_all_fixed_functions()
