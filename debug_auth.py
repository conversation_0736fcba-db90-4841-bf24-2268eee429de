#!/usr/bin/env python3
"""
Debug authentication issues by testing exact XML structure from topup-req-resp (4).xml
"""

import requests

# Configuration
BASE_URL = "http://41.203.65.10:8913/topupservice/service"
RESELLER_ID = "DIST2348077469471"
USER_ID = "9900"
TEST_MSISDN = "2348059999008"

def test_with_exact_xml_structure(password):
    """Test with exact XML structure from topup-req-resp (4).xml"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8'
        # No SOAPAction header since approach 1 worked
    }
    
    # Exact XML structure from topup-req-resp (4).xml with proper spacing
    soap_body = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ext="http://external.interfaces.ers.seamless.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ext:requestTopup>
         <context>
            <channel>WSClient</channel>
            <clientComment>test xml for ers</clientComment>
            <clientId>ERS</clientId>
            <clientReference>123456789</clientReference>
            <clientRequestTimeout>500</clientRequestTimeout>
            <initiatorPrincipalId>
               <id>{RESELLER_ID}</id>
               <type>RESELLERUSER</type>
               <userId>{USER_ID}</userId>
            </initiatorPrincipalId>
            <password>{password}</password>
         </context>
         <senderPrincipalId>
            <id>{RESELLER_ID} </id>
            <type>RESELLERUSER</type>
            <userId>{USER_ID}</userId>
         </senderPrincipalId>
         <topupPrincipalId>
            <id>{TEST_MSISDN}</id>
            <type>SUBSCRIBERMSISDN</type>
            <userId>?</userId>
         </topupPrincipalId>
         <senderAccountSpecifier>
            <accountId>{RESELLER_ID} </accountId>
            <accountTypeId>RESELLER</accountTypeId>
         </senderAccountSpecifier>
         <topupAccountSpecifier>
            <accountId>{TEST_MSISDN}</accountId>
            <accountTypeId>AIRTIME</accountTypeId>
         </topupAccountSpecifier>
         <productId>TOPUP</productId>
         <amount>
            <currency>NGN</currency>
            <value>1</value>
         </amount>
      </ext:requestTopup>
   </soapenv:Body>
</soapenv:Envelope>"""
    
    response = requests.post(BASE_URL, data=soap_body, headers=headers)
    return response

def test_different_passwords():
    """Test with different possible password formats"""
    
    passwords_to_try = [
        "mmt123mmt234",      # Current password
        "MMT123MMT234",      # Uppercase
        "mmt123mmt234",      # Lowercase (same as current)
        "Mmt123Mmt234",      # Title case
        "",                  # Empty password
        "123456",            # Simple password
        "password",          # Default password
        "admin",             # Admin password
    ]
    
    for i, password in enumerate(passwords_to_try, 1):
        print(f"\n{i}. Testing password: '{password}'")
        try:
            response = test_with_exact_xml_structure(password)
            print(f"Status: {response.status_code}")
            
            # Parse response to check result code
            if "resultCode>0<" in response.text:
                print("✅ SUCCESS! Authentication worked!")
                print(f"Response: {response.text}")
                return password
            elif "AUTHENTICATION_FAILED" in response.text:
                print("❌ Authentication failed")
            else:
                print(f"Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"Error: {e}")
    
    return None

if __name__ == "__main__":
    print("🔍 Debugging Authentication Issues")
    print("=" * 50)
    print(f"Base URL: {BASE_URL}")
    print(f"Reseller ID: {RESELLER_ID}")
    print(f"User ID: {USER_ID}")
    print(f"Test MSISDN: {TEST_MSISDN}")
    
    # Test current password first
    print(f"\n🧪 Testing current password: 'mmt123mmt234'")
    response = test_with_exact_xml_structure("mmt123mmt234")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    if "AUTHENTICATION_FAILED" in response.text:
        print(f"\n🔄 Current password failed. Trying alternatives...")
        working_password = test_different_passwords()
        
        if working_password:
            print(f"\n🎉 Found working password: '{working_password}'")
        else:
            print(f"\n❌ No working password found. You may need to:")
            print("   1. Contact the service provider for correct credentials")
            print("   2. Check if the reseller account is active")
            print("   3. Verify the reseller ID is correct")
    else:
        print(f"\n✅ Authentication successful with current password!")
