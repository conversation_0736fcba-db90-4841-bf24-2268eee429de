#!/usr/bin/env python3
"""
Test script to run on server with VPN access to ************:8913
This should work exactly like the curl command when run from the correct server environment.
"""

import requests
import json
from datetime import datetime

# Import the individual request functions
from individual_requests import (
    airtime_topup_1_naira,
    data_bundle_25_naira,
    vos_purchase_3_naira,
    vot_purchase_3_naira
)

def test_function_with_details(func_name, func):
    """Test a function and provide detailed output"""
    print(f"\n{'='*60}")
    print(f"🧪 Testing: {func_name}")
    print(f"{'='*60}")
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        print("📡 Sending request...")
        response = func()
        
        print(f"✅ Request completed!")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response Headers:")
        for key, value in response.headers.items():
            print(f"   {key}: {value}")
        
        print(f"\n📄 Response Body:")
        print(response.text)
        
        # Try to parse if it's a SOAP response
        if "soap:Envelope" in response.text or "soapenv:Envelope" in response.text:
            print(f"\n🧼 SOAP Response Detected!")
            if "resultCode" in response.text:
                # Extract result code
                import re
                result_code_match = re.search(r'<resultCode>(\d+)</resultCode>', response.text)
                if result_code_match:
                    result_code = result_code_match.group(1)
                    print(f"📈 Result Code: {result_code}")
                    
                    if result_code == "0":
                        print("🎉 SUCCESS! Transaction completed successfully!")
                    elif result_code == "2016":
                        print("ℹ️  Transaction already completed (duplicate)")
                    else:
                        print(f"⚠️  Transaction failed with code: {result_code}")
                
                # Extract result description
                desc_match = re.search(r'<resultDescription>(.*?)</resultDescription>', response.text)
                if desc_match:
                    description = desc_match.group(1)
                    print(f"📝 Description: {description}")
        
        return True, response
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection Error: {e}")
        print("🔍 This suggests the server is not accessible from this environment.")
        print("💡 Make sure you're running this from a server with VPN access to ************")
        return False, None
        
    except requests.exceptions.Timeout as e:
        print(f"⏰ Timeout Error: {e}")
        print("🔍 The server didn't respond within the timeout period.")
        return False, None
        
    except Exception as e:
        print(f"💥 Unexpected Error: {e}")
        print(f"🔍 Error Type: {type(e).__name__}")
        return False, None

def run_all_tests():
    """Run all test functions"""
    
    print("🚀 Glovas API Test Suite")
    print("=" * 60)
    print("🌐 Target Server: http://************:8913/topupservice/service")
    print("🔐 Reseller: DIST2348077469471 (Whispa Connect Limited)")
    print("📱 Test MSISDN: 2348059999008")
    print("💰 Test Amounts: Airtime=1₦, Data=25₦, VOS=3₦, VOT=3₦")
    
    # Test functions
    test_functions = [
        ("Airtime Topup (1 Naira)", airtime_topup_1_naira),
        ("Data Bundle (25 Naira)", data_bundle_25_naira),
        ("VOS Purchase (3 Naira)", vos_purchase_3_naira),
        ("VOT Purchase (3 Naira)", vot_purchase_3_naira),
    ]
    
    results = []
    
    for name, func in test_functions:
        success, response = test_function_with_details(name, func)
        results.append((name, success, response))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST RESULTS SUMMARY")
    print(f"{'='*60}")
    
    successful_tests = 0
    for name, success, response in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if success:
            successful_tests += 1
    
    print(f"\n🎯 Success Rate: {successful_tests}/{len(test_functions)} ({successful_tests/len(test_functions)*100:.1f}%)")
    
    if successful_tests == len(test_functions):
        print("🎉 All tests passed! Python functions are working correctly.")
    elif successful_tests > 0:
        print("⚠️  Some tests passed. Check failed tests for issues.")
    else:
        print("❌ All tests failed. Check network connectivity and VPN access.")
    
    return results

def test_single_function(function_name):
    """Test a single function by name"""
    
    function_map = {
        'airtime': ("Airtime Topup (1 Naira)", airtime_topup_1_naira),
        'data': ("Data Bundle (25 Naira)", data_bundle_25_naira),
        'vos': ("VOS Purchase (3 Naira)", vos_purchase_3_naira),
        'vot': ("VOT Purchase (3 Naira)", vot_purchase_3_naira),
    }
    
    if function_name.lower() in function_map:
        name, func = function_map[function_name.lower()]
        success, response = test_function_with_details(name, func)
        return success, response
    else:
        print(f"❌ Unknown function: {function_name}")
        print(f"Available functions: {', '.join(function_map.keys())}")
        return False, None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Test specific function
        function_name = sys.argv[1]
        test_single_function(function_name)
    else:
        # Run all tests
        run_all_tests()
    
    print(f"\n💡 Usage:")
    print(f"   python server_test.py           # Run all tests")
    print(f"   python server_test.py airtime   # Test airtime only")
    print(f"   python server_test.py data      # Test data bundle only")
    print(f"   python server_test.py vos       # Test VOS only")
    print(f"   python server_test.py vot       # Test VOT only")
